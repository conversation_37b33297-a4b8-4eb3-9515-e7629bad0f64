# MySQL Database Project

这是一个Node.js项目，用于连接MySQL数据库并执行查询和插入操作。

## 功能

1. 连接到MySQL数据库 `hz_asian_games_club`
2. 查询表 `asian_tribe_assignment_challenge` 中 `assignment_id=17` 且 `games_integral` 最大的数据
3. 向表中插入两条指定的数据记录

## 安装依赖

```bash
npm install
```

## 配置数据库

1. 复制 `.env` 文件并修改数据库连接信息：
   - `DB_HOST`: 数据库主机地址
   - `DB_PORT`: 数据库端口
   - `DB_USER`: 数据库用户名
   - `DB_PASSWORD`: 数据库密码
   - `DB_NAME`: 数据库名称

## 运行项目

```bash
# 直接运行
npm start

# 开发模式（自动重启）
npm run dev
```

## 数据库表结构

表名：`asian_tribe_assignment_challenge`

字段：
- `id` - 主键ID
- `user_id` - 用户ID
- `assignment_id` - 任务ID
- `integral_status` - 积分状态
- `energy_value` - 能量值
- `games_integral` - 游戏积分
- `create_time` - 创建时间
- `update_time` - 更新时间
- `del_flag` - 删除标志

## 注意事项

1. 请确保MySQL数据库服务正在运行
2. 请确保数据库连接信息正确
3. 请确保有足够的权限执行查询和插入操作
4. 插入数据时请注意ID冲突问题
