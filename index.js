const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
};

// 创建数据库连接
async function createConnection() {
  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('数据库连接成功！');
    return connection;
  } catch (error) {
    console.error('数据库连接失败:', error);
    throw error;
  }
}

// 查询assignment_id=17，games_integral最大的数据
async function queryMaxGamesIntegral(connection) {
  try {
    const query = `
      SELECT * FROM asian_tribe_assignment_challenge 
      WHERE assignment_id = 17 
      ORDER BY games_integral DESC 
      LIMIT 1
    `;
    
    const [rows] = await connection.execute(query);
    console.log('查询结果 - assignment_id=17，games_integral最大的数据:');
    console.log(rows);
    return rows;
  } catch (error) {
    console.error('查询失败:', error);
    throw error;
  }
}

// 插入数据
async function insertData(connection) {
  try {
    // 第一条插入数据
    const insertQuery1 = `
      INSERT INTO asian_tribe_assignment_challenge
      (id, user_id, assignment_id, integral_status, energy_value, games_integral, create_time, update_time, del_flag)
      VALUES (258449, 44294, 17, 3, 0, 889, '2025-08-25 09:16:00', '2025-08-25 09:16:03', 0)
    `;
    
    // 第二条插入数据
    const insertQuery2 = `
      INSERT INTO asian_tribe_assignment_challenge
      (id, user_id, assignment_id, integral_status, energy_value, games_integral, create_time, update_time, del_flag)
      VALUES (258450, 44294, 20, 3, 1, 889, '2025-08-25 09:16:04', '2025-08-25 09:16:06', 0)
    `;
    
    // 执行第一条插入
    const [result1] = await connection.execute(insertQuery1);
    console.log('第一条数据插入成功:', result1);
    
    // 执行第二条插入
    const [result2] = await connection.execute(insertQuery2);
    console.log('第二条数据插入成功:', result2);
    
    return { result1, result2 };
  } catch (error) {
    console.error('数据插入失败:', error);
    throw error;
  }
}

// 主函数
async function main() {
  let connection;
  
  try {
    // 创建数据库连接
    connection = await createConnection();
    
    // 查询数据
    console.log('\n=== 开始查询数据 ===');
    await queryMaxGamesIntegral(connection);
    
    // 插入数据
    console.log('\n=== 开始插入数据 ===');
    await insertData(connection);
    
    console.log('\n=== 所有操作完成 ===');
    
  } catch (error) {
    console.error('程序执行出错:', error);
  } finally {
    // 关闭数据库连接
    if (connection) {
      await connection.end();
      console.log('数据库连接已关闭');
    }
  }
}

// 运行主函数
main();
